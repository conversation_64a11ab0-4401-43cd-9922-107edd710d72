0000000000000000000000000000000000000000 a6a24d72d7fbc92765a9ca610ab219bd1664f997 <PERSON><PERSON><PERSON> <<EMAIL>> 1748006903 +0530	branch: Created from HEAD
a6a24d72d7fbc92765a9ca610ab219bd1664f997 a5e19227009e5b08f1593055b57e37ce42a05eca Pratham Agarwal <<EMAIL>> 1748006950 +0530	commit: Refactor workflow components and add context and result models
a5e19227009e5b08f1593055b57e37ce42a05eca 273664681d36e965840bca3ed1b2e63056ffcc2f Pratham Agarwal <<EMAIL>> 1748007160 +0530	commit: Deeleted readme
273664681d36e965840bca3ed1b2e63056ffcc2f ba54c96ad468bce2275ebedac3780f4e9676ada9 <PERSON>ratham <PERSON> <<EMAIL>> 1748080179 +0530	commit: add property-based field matching tests for SelectDataComponent
ba54c96ad468bce2275ebedac3780f4e9676ada9 1e5b1cf7d8a6044a6bd3629a7e3394d09ea48517 Pratham Agarwal <<EMAIL>> 1748080449 +0530	commit (merge): Select data component fixed
1e5b1cf7d8a6044a6bd3629a7e3394d09ea48517 747b6002cfa56536bd492c393255f4b19df4961e Pratham Agarwal <<EMAIL>> 1748256313 +0530	commit: Refactor processing components: remove ParseJSONData and UpdateData components, update SplitText to use async execute method, and enhance error handling. Add comprehensive tests for AlterMetadataComponent and ApiRequestNode, ensuring dual-purpose input functionality and validation checks. Introduce new test scripts for workflow service integration and component definition verification.
747b6002cfa56536bd492c393255f4b19df4961e 99f883417dfdb4124589684317f42f26f4eae47d Pratham Agarwal <<EMAIL>> ********** +0530	commit: Refactor DataToDataFrameComponent and MessageToDataComponent: unify input handling with dual-purpose inputs, enhance logging, and improve error handling. Add comprehensive tests for DataToDataFrameComponent, including end-to-end scenarios and performance validation.
99f883417dfdb4124589684317f42f26f4eae47d 8b02b01860e7a0d6798a0c243d287a98e73c9aaa Pratham Agarwal <<EMAIL>> ********** +0530	commit: feat: Implement ConditionalNode with evaluation logic and routing
