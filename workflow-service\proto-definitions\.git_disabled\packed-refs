# pack-refs with: peeled fully-peeled sorted 
a4936f8c20302c685ebaf9e4b89cd137b741a8b4 refs/remotes/origin/ARW-109-develop-communication-microservice
c513b4bb66dd0ee26dbdc91c83612183f0181e5d refs/remotes/origin/ARW-122-implement-endpoint-for-google-and-update-password-in-api-gateway
94e24ff0a3fcf97ddd903536c5bd9f8af69d3878 refs/remotes/origin/ARW-127-create-and-add-pr-for-agent-service-proto-definitions
54dc9e4f9dbaa5615cf83425fe551c5cfd8439bf refs/remotes/origin/ARW-158-update-communication-proto-file-remove-agent-id-from-conversation-model
b9a67a01d374e2c8df148526702039e7181a4dd6 refs/remotes/origin/ARW-165-create-a-proto-file-for-workflow-builder-service
2530aec2f2a0bcb7c56f61cc1e988001d8b898d5 refs/remotes/origin/ARW-217-create-a-proto-file-for-mcp-server-registry-microservice
0ea54117e3ccad8f301cda5393115a83bf60e288 refs/remotes/origin/ARW-230-update-user-proto-file-to-add-organization-related-methods
a355d136ba60f296f08845bfa2e237104b84ff48 refs/remotes/origin/ARW-300-update-proto-definitions-to-add-waitlist-rpcs
9528bf03132415cec3639fa68b5cd5a0d72640f9 refs/remotes/origin/ARW-355-update-user-proto-definition-file-according-to-organization-waitlist-rpcs
349bd3077660d6818964d409c29ecf8dc8a0e351 refs/remotes/origin/ARW-356-create-appnotification-proto-file-to-fetch-users-notifications
68a976ba025783ae48f651647538516ed9939a14 refs/remotes/origin/ARW-371-update-user-proto-file-with-the-new-rpcs-for-api-key-management
02534abab56463d51281bb5269f72cc060f6c054 refs/remotes/origin/ARW-373-update-proto-definitions-based-on-the-new-patch-agent-routes
cdb02f5b4daf04e66a4e15b707a8fad41876376e refs/remotes/origin/ARW-381-update-workflow-proto-rpcs-for-workflow-builder-service
8889831ceed05cec3c99e2c088f0027ef5119e5c refs/remotes/origin/ARW-382-update-agent-proto-rpcs-for-agent-builder-service
3e7d739c91be8aa505470994a302215fa69dc7a1 refs/remotes/origin/ARW-388-update-mcp-proto-based-on-new-mcp-schema-template-schema
073bb98338c5f97b62549d0b2c62cd8f3b578c2c refs/remotes/origin/ARW-396-implement-rpcs-for-credential-manager-in-user-proto
b0ca9756a8c9ad40a7ca2815b7b8b8f7ea4b304d refs/remotes/origin/ARW-432-update-add-new-rpcs-in-workflow-proto-definitions
7bc502f53a641062609107e2b91096e10b9bcad1 refs/remotes/origin/ARW-445-add-required-proto-rpcs-in-workflow-proto
3f9a5f12ec2f1bba4ca7ca4e66ecc902cd18a328 refs/remotes/origin/ARW-458-communication-service-updated-schema
844ee0ab13ed16c3b44b08e81662c0186f8da03a refs/remotes/origin/ARW-464-update-the-proto-rpcs-according-to-mcp-service-schema-remove-template-related-rpcs
7342672adde45ba28811298cc330f20e8b11255f refs/remotes/origin/ARW-483-add-agent-avatar-management-rpcs-in-agent-proto-file
ac0296e33aaf80debc4ac8d3dc145c18b0390c87 refs/remotes/origin/ARW-487-add-a-list-user-by-user-ids-proto-rpc-in-user-proto
edb334cafcd19ce3b81c8e6d607f0134b9eb2ac3 refs/remotes/origin/ARW-497-update-agent-proto-definitions-to-include-new-agent-capabilities-fields-example-prompts
2dfb0a6ce4905e362634b3323a5e7a9bbdd5505b refs/remotes/origin/ARW-515-update-mcp-user-proto-definitions-to-include-github-access-token-storage-capability-and-more
c14eee6e775faaff7c563fc469b0003164ed6d6d refs/remotes/origin/ARW-533-update-workflow-proto-based-on-new-versioning-rpcs-and-messages-types
24d0394b22271c26ab425fec7cd9eebb5df37bfa refs/remotes/origin/admin-endpoints
967b018513df345db51cb6549477cd60d614725b refs/remotes/origin/agent-service
b8bb0fb80101fc82df04c7e2ec5f4ecd92343ca9 refs/remotes/origin/dev
5730b8527b93e7c89f94e58c2b0c3e0ca33588f3 refs/remotes/origin/improvements
967b018513df345db51cb6549477cd60d614725b refs/remotes/origin/main
9118a8c10699a76c721f014f002771a585540260 refs/remotes/origin/workflow-service
