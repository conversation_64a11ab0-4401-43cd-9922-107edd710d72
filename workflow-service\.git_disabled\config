[core]
	repositoryformatversion = 0
	filemode = false
	bare = false
	logallrefupdates = true
	symlinks = false
	ignorecase = true
[remote "origin"]
	url = *******************************:ruh.ai/workflow-service.git
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "ARW-367-convert-schema-1-into-schema-2-on-the-workflow-builder-service"]
	remote = origin
	merge = refs/heads/ARW-367-convert-schema-1-into-schema-2-on-the-workflow-builder-service
	vscode-merge-base = origin/main
[branch "ARW-442"]
	remote = origin
	merge = refs/heads/ARW-442-migrate-workflow-builder-backend-into-existing-workflow-service
	vscode-merge-base = origin/ARW-442-migrate-workflow-builder-backend-into-existing-workflow-service
[branch "xmergey"]
	vscode-merge-base = origin/ARW-442-migrate-workflow-builder-backend-into-existing-workflow-service
[branch "dev"]
	remote = origin
	merge = refs/heads/dev
	vscode-merge-base = origin/dev
[branch "refactor-workflow-components"]
	vscode-merge-base = origin/ARW-442-migrate-workflow-builder-backend-into-existing-workflow-service
	remote = origin
	merge = refs/heads/refactor-workflow-components
[branch "ARW-530-fix-issue-with-data-lineage-while-traversing-duplicate-nodes-nodes-with-similiar-output"]
	vscode-merge-base = origin/refactor-workflow-components
