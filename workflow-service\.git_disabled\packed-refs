# pack-refs with: peeled fully-peeled sorted 
5714613859335f8a8b05f83cee8e661044a6186e refs/remotes/origin/ARW-123-develop-workflow-builder-microservice
544456ccb6136f8d8dcd833f2033b04d9e9325a7 refs/remotes/origin/ARW-333-enhance-workflow-builder-microservice-with-suggested-changes
4ef05ea3b62bb122fe265ea749ad33dda0c857cd refs/remotes/origin/ARW-367-convert-schema-1-into-schema-2-on-the-workflow-builder-service
e338c88018f270e94241535deb6ad4d4142a0387 refs/remotes/origin/ARW-377-update-workflow-builder-service-wrt-updated-workflow-schema-workflow-template
03cf5ab890bed1e8d9c3756b1a231f553ecbdfa5 refs/remotes/origin/ARW-416-update-the-workflow-service-with-new-fields-according-to-template-table
9f0ebe36da439f7295caec3c3cbf3750515ec31f refs/remotes/origin/dev
6e8e7dc8dcfa6efa75a3712749461889d4ca4e53 refs/remotes/origin/main
1a9208d49edfb4f691a0c4dd48a72575ffdeacd4 refs/remotes/origin/revert-5b400e3d
affa5a921e2ae7345228a4783245c5b40db0d1c4 refs/remotes/origin/temp-branch
