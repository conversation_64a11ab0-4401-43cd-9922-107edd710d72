0000000000000000000000000000000000000000 4ef05ea3b62bb122fe265ea749ad33dda0c857cd Pratham <PERSON> <<EMAIL>> 1747038720 +0530	clone: from gitlab.rapidinnovation.tech:ruh.ai/workflow-service.git
4ef05ea3b62bb122fe265ea749ad33dda0c857cd d0313837618d476b89cdd08b6fbf4ebf9289ab1a Pratham <PERSON>wal <<EMAIL>> 1747130267 +0530	commit: conditional node added
d0313837618d476b89cdd08b6fbf4ebf9289ab1a 1253b8cb0976c409ead52b52b0b26f622b3837ce Pratham Agarwal <<EMAIL>> 1747135062 +0530	checkout: moving from ARW-367-convert-schema-1-into-schema-2-on-the-workflow-builder-service to ARW-442
1253b8cb0976c409ead52b52b0b26f622b3837ce 1253b8cb0976c409ead52b52b0b26f622b3837ce Pratham Agarwal <<EMAIL>> 1747135433 +0530	checkout: moving from ARW-442 to ARW-442
1253b8cb0976c409ead52b52b0b26f622b3837ce 018ec9a2f1f393ce431bccd7146dc400a2d89a48 Pratham Agarwal <<EMAIL>> 1747316838 +0530	commit: ARW-442 Enhance component discovery and input/output handling in workflow builder service
018ec9a2f1f393ce431bccd7146dc400a2d89a48 018ec9a2f1f393ce431bccd7146dc400a2d89a48 Pratham Agarwal <<EMAIL>> 1747316878 +0530	checkout: moving from ARW-442 to xmergey
018ec9a2f1f393ce431bccd7146dc400a2d89a48 35f70ec226967ed6fdd045100f66ae1109b378b4 Pratham Agarwal <<EMAIL>> 1747317678 +0530	commit (merge): feat: Add scripts for inserting dummy templates and workflows, and verifying workflows
35f70ec226967ed6fdd045100f66ae1109b378b4 9886b8560e6e744b9b033cb9ab94ad4a936807c1 Pratham Agarwal <<EMAIL>> 1747317706 +0530	commit: Implement feature X to enhance user experience and optimize performance
9886b8560e6e744b9b033cb9ab94ad4a936807c1 018ec9a2f1f393ce431bccd7146dc400a2d89a48 Pratham Agarwal <<EMAIL>> 1747317810 +0530	checkout: moving from xmergey to ARW-442
018ec9a2f1f393ce431bccd7146dc400a2d89a48 9886b8560e6e744b9b033cb9ab94ad4a936807c1 Pratham Agarwal <<EMAIL>> 1747317827 +0530	merge xmergey: Fast-forward
9886b8560e6e744b9b033cb9ab94ad4a936807c1 7b8d1b233f36ddb87defb66cc2be53ad7e4ac559 Pratham Agarwal <<EMAIL>> 1747376735 +0530	commit: feat: Remove owner_name from workflow creation and retrieval for consistency
7b8d1b233f36ddb87defb66cc2be53ad7e4ac559 29daafe166fdc6df7a8c7e265bae3e9f863e02e9 Pratham Agarwal <<EMAIL>> 1747390856 +0530	commit: feat: Add discoverComponents method to WorkflowService for component discovery
29daafe166fdc6df7a8c7e265bae3e9f863e02e9 29daafe166fdc6df7a8c7e265bae3e9f863e02e9 Pratham Agarwal <<EMAIL>> 1747893316 +0530	reset: moving to HEAD
29daafe166fdc6df7a8c7e265bae3e9f863e02e9 26878774738feaf22198a0b080a9ef4fd5d7600c Pratham Agarwal <<EMAIL>> 1747893330 +0530	checkout: moving from ARW-442 to dev
26878774738feaf22198a0b080a9ef4fd5d7600c cfe3632edf7bf89d572ad1d575e795202c2bb74b Pratham Agarwal <<EMAIL>> 1747893338 +0530	pull origin dev: Fast-forward
cfe3632edf7bf89d572ad1d575e795202c2bb74b 29daafe166fdc6df7a8c7e265bae3e9f863e02e9 Pratham Agarwal <<EMAIL>> 1747893344 +0530	checkout: moving from dev to ARW-442
29daafe166fdc6df7a8c7e265bae3e9f863e02e9 cfe3632edf7bf89d572ad1d575e795202c2bb74b Pratham Agarwal <<EMAIL>> 1747893356 +0530	reset: moving to dev
cfe3632edf7bf89d572ad1d575e795202c2bb74b a6a24d72d7fbc92765a9ca610ab219bd1664f997 Pratham Agarwal <<EMAIL>> 1747909791 +0530	pull origin dev: Fast-forward
a6a24d72d7fbc92765a9ca610ab219bd1664f997 a6a24d72d7fbc92765a9ca610ab219bd1664f997 Pratham Agarwal <<EMAIL>> 1748006903 +0530	checkout: moving from ARW-442 to refactor-workflow-components
a6a24d72d7fbc92765a9ca610ab219bd1664f997 a5e19227009e5b08f1593055b57e37ce42a05eca Pratham Agarwal <<EMAIL>> 1748006950 +0530	commit: Refactor workflow components and add context and result models
a5e19227009e5b08f1593055b57e37ce42a05eca 273664681d36e965840bca3ed1b2e63056ffcc2f Pratham Agarwal <<EMAIL>> 1748007160 +0530	commit: Deeleted readme
273664681d36e965840bca3ed1b2e63056ffcc2f ba54c96ad468bce2275ebedac3780f4e9676ada9 Pratham Agarwal <<EMAIL>> 1748080179 +0530	commit: add property-based field matching tests for SelectDataComponent
ba54c96ad468bce2275ebedac3780f4e9676ada9 1e5b1cf7d8a6044a6bd3629a7e3394d09ea48517 Pratham Agarwal <<EMAIL>> 1748080449 +0530	commit (merge): Select data component fixed
1e5b1cf7d8a6044a6bd3629a7e3394d09ea48517 747b6002cfa56536bd492c393255f4b19df4961e Pratham Agarwal <<EMAIL>> 1748256313 +0530	commit: Refactor processing components: remove ParseJSONData and UpdateData components, update SplitText to use async execute method, and enhance error handling. Add comprehensive tests for AlterMetadataComponent and ApiRequestNode, ensuring dual-purpose input functionality and validation checks. Introduce new test scripts for workflow service integration and component definition verification.
747b6002cfa56536bd492c393255f4b19df4961e 99f883417dfdb4124589684317f42f26f4eae47d Pratham Agarwal <<EMAIL>> ********** +0530	commit: Refactor DataToDataFrameComponent and MessageToDataComponent: unify input handling with dual-purpose inputs, enhance logging, and improve error handling. Add comprehensive tests for DataToDataFrameComponent, including end-to-end scenarios and performance validation.
99f883417dfdb4124589684317f42f26f4eae47d 8b02b01860e7a0d6798a0c243d287a98e73c9aaa Pratham Agarwal <<EMAIL>> ********** +0530	commit: feat: Implement ConditionalNode with evaluation logic and routing
8b02b01860e7a0d6798a0c243d287a98e73c9aaa 8b02b01860e7a0d6798a0c243d287a98e73c9aaa Pratham Agarwal <<EMAIL>> ********** +0530	checkout: moving from refactor-workflow-components to ARW-530-fix-issue-with-data-lineage-while-traversing-duplicate-nodes-nodes-with-similiar-output
8b02b01860e7a0d6798a0c243d287a98e73c9aaa 8b02b01860e7a0d6798a0c243d287a98e73c9aaa Pratham Agarwal <<EMAIL>> ********** +0530	pull origin ARW-530-fix-issue-with-data-lineage-while-traversing-duplicate-nodes-nodes-with-similiar-output: updating HEAD
8b02b01860e7a0d6798a0c243d287a98e73c9aaa 8b02b01860e7a0d6798a0c243d287a98e73c9aaa Pratham Agarwal <<EMAIL>> ********** +0530	pull origin ARW-530-fix-issue-with-data-lineage-while-traversing-duplicate-nodes-nodes-with-similiar-output --force: updating HEAD
8b02b01860e7a0d6798a0c243d287a98e73c9aaa 8b02b01860e7a0d6798a0c243d287a98e73c9aaa Pratham Agarwal <<EMAIL>> ********** +0530	reset: moving to HEAD
8b02b01860e7a0d6798a0c243d287a98e73c9aaa 8b02b01860e7a0d6798a0c243d287a98e73c9aaa Pratham Agarwal <<EMAIL>> 1748353723 +0530	reset: moving to HEAD
8b02b01860e7a0d6798a0c243d287a98e73c9aaa 8b02b01860e7a0d6798a0c243d287a98e73c9aaa Pratham Agarwal <<EMAIL>> 1748353944 +0530	reset: moving to HEAD
8b02b01860e7a0d6798a0c243d287a98e73c9aaa bc5f0edda5711740ae01376e8f9e77ade8f01bc2 Pratham Agarwal <<EMAIL>> 1748353968 +0530	pull origin ARW-530-fix-issue-with-data-lineage-while-traversing-duplicate-nodes-nodes-with-similiar-output: Merge made by the 'ort' strategy.
bc5f0edda5711740ae01376e8f9e77ade8f01bc2 7ed7595ccd5d799f12c9604992c854befb1b3040 Pratham Agarwal <<EMAIL>> 1748428810 +0530	commit: conditional node
7ed7595ccd5d799f12c9604992c854befb1b3040 f9de8b726a6895a398ed53f798e6e10a16e1302a Pratham Agarwal <<EMAIL>> 1748428905 +0530	pull origin ARW-530-fix-issue-with-data-lineage-while-traversing-duplicate-nodes-nodes-with-similiar-output: Merge made by the 'ort' strategy.
f9de8b726a6895a398ed53f798e6e10a16e1302a de3bdcf387594a5155d28e79ce108d2fea123de1 Pratham Agarwal <<EMAIL>> 1748436481 +0530	commit: conditional node working
de3bdcf387594a5155d28e79ce108d2fea123de1 6b70b98b0c837c80de9f8e4ecb030f9100f1dd3e Pratham Agarwal <<EMAIL>> 1748436751 +0530	pull origin ARW-530-fix-issue-with-data-lineage-while-traversing-duplicate-nodes-nodes-with-similiar-output: Merge made by the 'ort' strategy.
